<script>
  import { onMount } from 'svelte';
  import { selectedFolder, viewerState } from '$lib/stores.js';
  import <PERSON> from 'macy';

  let photos = [];
  let error = null;
  let isLoading = false;
  let macyInstance = null;
  let gridElement;

  selectedFolder.subscribe(async (folder) => {
    if (!folder) {
      photos = [];
      return;
    }
    isLoading = true;
    error = null;
    try {
      const response = await fetch(`/api/photos?folder=${encodeURIComponent(folder.path)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch photos');
      }
      photos = await response.json();
      
      // 等待DOM更新后再初始化Macy
      setTimeout(rebuildMacy, 0);

    } catch (e) {
      error = e.message;
    } finally {
      isLoading = false;
    }
  });

  function rebuildMacy() {
    if (macyInstance) {
      macyInstance.recalculate(true);
    } else if (gridElement && photos.length > 0) {
      macyInstance = Macy({
        container: gridElement,
        trueOrder: false,
        waitForImages: false,
        margin: 10,
        columns: 5,
        breakAt: {
          1200: 4,
          940: 3,
          520: 2,
          400: 1
        }
      });
    }
  }

  onMount(() => {
    return () => {
      if (macyInstance) {
        macyInstance.remove();
      }
    };
  });
</script>

<div class="photo-grid-container">
  {#if isLoading}
    <p>Loading photos...</p>
  {:else if error}
    <p class="error">{error}</p>
  {:else if photos.length > 0}
    <div bind:this={gridElement} class="macy-grid">
      {#each photos as photo, index (photo.id)}
        <div class="grid-item" on:click={() => openViewer(index)}>
          <img src={photo.thumbnail_path} alt="Thumbnail" />
        </div>
      {/each}
    </div>
  {:else}
    <p>Select a folder to view photos.</p>
  {/if}
</div>

<style>
  .macy-grid {
    width: 100%;
  }
  .grid-item {
    margin-bottom: 10px;
  }
  .grid-item img {
    width: 100%;
    display: block;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  .error {
    color: red;
  }
  .grid-item {
    cursor: pointer;
  }
</style>

<script>
  function openViewer(index) {
    viewerState.set({
      isOpen: true,
      photos: photos,
      currentIndex: index,
    });
  }
</script>
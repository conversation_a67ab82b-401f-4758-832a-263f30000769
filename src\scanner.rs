use crate::thumbnail;
use rusqlite::Connection;
use std::path::Path;
use std::sync::{Arc, Mutex};
use walkdir::WalkDir;

const SUPPORTED_EXTENSIONS: &[&str] = &["jpg", "jpeg", "png", "gif", "webp"];

pub fn scan_directories(root_path: &str, db_pool: Arc<Mutex<Connection>>) {
    log::info!("Starting scan of directory: {}", root_path);
    let root = Path::new(root_path);

    if !root.is_dir() {
        log::error!("Provided path is not a directory: {}", root_path);
        return;
    }

    for entry in WalkDir::new(root).into_iter().filter_map(|e| e.ok()) {
        if entry.file_type().is_file() {
            if let Some(extension) = entry.path().extension().and_then(|s| s.to_str()) {
                if SUPPORTED_EXTENSIONS.contains(&extension.to_lowercase().as_str()) {
                    let image_path = entry.path();
                    match thumbnail::generate_thumbnail(image_path) {
                        Ok(thumbnail_path) => {
                            let conn = db_pool.lock().unwrap();
                            let folder_path = image_path.parent().unwrap().to_str().unwrap();
                            let file_path_str = image_path.to_str().unwrap();
                            let thumbnail_path_str = thumbnail_path.to_str().unwrap();

                            conn.execute(
                                "INSERT OR IGNORE INTO photos (file_path, folder_path, thumbnail_path) VALUES (?1, ?2, ?3)",
                                &[file_path_str, folder_path, thumbnail_path_str],
                            ).unwrap();
                        }
                        Err(e) => {
                            log::error!("Failed to generate thumbnail for {:?}: {}", image_path, e);
                        }
                    }
                }
            }
        }
    }
    log::info!("Scan finished for directory: {}", root_path);
}
<svelte:head>
  <title><PERSON><PERSON>er</title>
  <meta name="description" content="A high-performance local picture browser." />
</svelte:head>

<script>
  import Sidebar from '$lib/components/Sidebar.svelte';
  import PhotoGrid from '$lib/components/PhotoGrid.svelte';
  import Viewer from '$lib/components/Viewer.svelte';
</script>

<Viewer />

<div class="app-layout">
  <aside class="sidebar">
    <Sidebar />
  </aside>
  <main class="main-content">
    <PhotoGrid />
  </main>
</div>

<style>
  :global(body) {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
      Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f0f2f5;
  }

  .app-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    height: 100vh;
  }

  .sidebar {
    background-color: #fff;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
  }

  .main-content {
    overflow-y: auto;
    padding: 1rem;
  }
</style>
use image::GenericImageView;
use std::fs;
use std::path::{Path, PathBuf};
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};

const THUMBNAIL_DIR: &str = ".cache/thumbnails";
const THUMBNAIL_HEIGHT: u32 = 400;

pub fn generate_thumbnail(image_path: &Path) -> Result<PathBuf, Box<dyn std::error::Error>> {
    fs::create_dir_all(THUMBNAIL_DIR)?;

    let img = image::open(image_path)?;
    let (width, height) = img.dimensions();
    let new_width = (width as f32 * (THUMBNAIL_HEIGHT as f32 / height as f32)) as u32;

    let thumbnail = img.thumbnail(new_width, THUMBNAIL_HEIGHT);

    let mut hasher = DefaultHasher::new();
    image_path.hash(&mut hasher);
    let hash = hasher.finish();
    
    let extension = image_path.extension().and_then(|s| s.to_str()).unwrap_or("jpg");
    let thumbnail_file_name = format!("{}.{}", hash, extension);
    let thumbnail_path = Path::new(THUMBNAIL_DIR).join(thumbnail_file_name);

    thumbnail.save(&thumbnail_path)?;

    Ok(thumbnail_path)
}
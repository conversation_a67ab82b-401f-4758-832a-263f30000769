<script>
  import { onMount, onDestroy } from 'svelte';
  import { viewerState } from '$lib/stores.js';

  let currentPhoto = null;
  let currentIndex = -1;
  let photos = [];
  let isOpen = false;

  const unsubscribe = viewerState.subscribe(value => {
    isOpen = value.isOpen;
    photos = value.photos;
    currentIndex = value.currentIndex;
    if (isOpen && photos.length > 0) {
      currentPhoto = photos[currentIndex];
    }
  });

  function closeViewer() {
    viewerState.set({ isOpen: false, photos: [], currentIndex: -1 });
  }

  function navigate(direction) {
    let newIndex = currentIndex + direction;
    if (newIndex >= 0 && newIndex < photos.length) {
      viewerState.update(state => ({ ...state, currentIndex: newIndex }));
    }
  }

  function handleKeydown(event) {
    if (!isOpen) return;
    if (event.key === 'Escape') {
      closeViewer();
    } else if (event.key === 'ArrowLeft') {
      navigate(-1);
    } else if (event.key === 'ArrowRight') {
      navigate(1);
    }
  }

  onMount(() => {
    window.addEventListener('keydown', handleKeydown);
  });

  onDestroy(() => {
    window.removeEventListener('keydown', handleKeydown);
    unsubscribe();
  });

  function getPhotoUrl(path) {
    return `/photo/${btoa(path)}`;
  }
</script>

{#if isOpen && currentPhoto}
<div class="viewer-overlay" on:click={closeViewer}>
  <div class="viewer-content" on:click|stopPropagation>
    <button class="close-btn" on:click={closeViewer}>&times;</button>
    <button class="nav-btn prev" on:click={() => navigate(-1)} disabled={currentIndex === 0}><</button>
    
    <div class="image-container">
      <img src={getPhotoUrl(currentPhoto.original_path)} alt="Full size" />
      <!-- Preload next and previous images -->
      {#if currentIndex > 0}
        <link rel="prefetch" href={getPhotoUrl(photos[currentIndex - 1].original_path)} />
      {/if}
      {#if currentIndex < photos.length - 1}
        <link rel="prefetch" href={getPhotoUrl(photos[currentIndex + 1].original_path)} />
      {/if}
    </div>

    <button class="nav-btn next" on:click={() => navigate(1)} disabled={currentIndex === photos.length - 1}>></button>
  </div>
</div>
{/if}

<style>
  .viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  .viewer-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
  }
  .image-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .image-container img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
  }
  .close-btn, .nav-btn {
    position: absolute;
    background: rgba(0,0,0,0.5);
    color: white;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    z-index: 1001;
  }
  .close-btn {
    top: 10px;
    right: 10px;
    padding: 0 1rem;
  }
  .nav-btn {
    top: 50%;
    transform: translateY(-50%);
  }
  .prev { left: 10px; }
  .next { right: 10px; }
</style>
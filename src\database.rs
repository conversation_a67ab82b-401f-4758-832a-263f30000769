use rusqlite::{Connection, Result};

pub fn initialize_database() -> Result<Connection> {
    let conn = Connection::open("database.db")?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS photos (
            id             INTEGER PRIMARY KEY,
            file_path      TEXT NOT NULL UNIQUE,
            folder_path    TEXT NOT NULL,
            thumbnail_path TEXT NOT NULL
        )",
        [],
    )?;

    Ok(conn)
}
# ---- Builder Stage: Frontend ----
FROM node:20-slim as frontend-builder

WORKDIR /app/frontend

# Copy frontend package files and install dependencies
COPY frontend/package.json frontend/package-lock.json* ./
RUN npm install

# Copy the rest of the frontend source code and build
COPY frontend/ ./
RUN npm run build

# ---- Builder Stage: Backend ----
FROM rust:1.81 as backend-builder

WORKDIR /app

# Create a dummy project to cache dependencies
RUN cargo init --bin .
COPY Cargo.toml ./
RUN cargo build --release
RUN rm -rf src target/release/deps/pic_browser*

# Copy the actual source code and build
COPY ./src ./src
RUN cargo build --release

# ---- Final Stage ----
FROM debian:bookworm-slim

WORKDIR /app

# Copy backend executable from builder
COPY --from=backend-builder /app/target/release/pic-browser .

# Copy frontend static files from builder
COPY --from=frontend-builder /app/frontend/build ./frontend/build

# Expose the application port
EXPOSE 3000

# Set the entrypoint
CMD ["./pic-browser"]
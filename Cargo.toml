[package]
name = "pic-browser"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "0.7"
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rusqlite = { version = "0.31", features = ["bundled"] }
image = "0.25"
tower-http = { version = "0.5", features = ["fs", "trace"] }
log = "0.4"
env_logger = "0.11"
base64 = "0.22"
walkdir = "2"
mime_guess = "2.0"
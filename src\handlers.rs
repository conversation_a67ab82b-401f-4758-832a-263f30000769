use axum::{
    extract::{Path, State},
    http::{header, StatusCode},
    response::{IntoResponse, Response},
    Json,
};
use base64::{engine::general_purpose, Engine as _};
use rusqlite::Connection;
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};
use axum::extract::Query;

#[derive(Serialize)]
pub struct Folder {
    path: String,
    cover_path: String,
}

pub async fn get_folders(
    State(db_pool): State<Arc<Mutex<Connection>>>,
) -> Result<Json<Vec<Folder>>, StatusCode> {
    let conn = db_pool.lock().unwrap();
    let mut stmt = conn
        .prepare(
            "SELECT folder_path, MIN(thumbnail_path) 
             FROM photos 
             GROUP BY folder_path 
             ORDER BY folder_path ASC",
        )
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let folder_iter = stmt
        .query_map([], |row| {
            Ok(Folder {
                path: row.get(0)?,
                cover_path: row.get(1)?,
            })
        })
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let mut folders = Vec::new();
    for folder in folder_iter {
        folders.push(folder.unwrap());
    }

    Ok(Json(folders))
}

#[derive(Deserialize)]
pub struct PhotosQuery {
    folder: String,
}

#[derive(Serialize)]
pub struct Photo {
    id: i32,
    original_path: String,
    thumbnail_path: String,
}

pub async fn get_photos(
    State(db_pool): State<Arc<Mutex<Connection>>>,
    Query(query): Query<PhotosQuery>,
) -> Result<Json<Vec<Photo>>, StatusCode> {
    let conn = db_pool.lock().unwrap();
    let mut stmt = conn
        .prepare(
            "SELECT id, file_path, thumbnail_path
             FROM photos
             WHERE folder_path = ?1
             ORDER BY file_path ASC",
        )
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let photo_iter = stmt
        .query_map([query.folder], |row| {
            Ok(Photo {
                id: row.get(0)?,
                original_path: row.get(1)?,
                thumbnail_path: row.get(2)?,
            })
        })
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    let mut photos = Vec::new();
    for photo in photo_iter {
        photos.push(photo.unwrap());
    }

    Ok(Json(photos))
}

pub async fn get_photo(Path(encoded_path): Path<String>) -> impl IntoResponse {
    let decoded_path = match general_purpose::URL_SAFE_NO_PAD.decode(encoded_path) {
        Ok(path_bytes) => match String::from_utf8(path_bytes) {
            Ok(path) => path,
            Err(_) => return (StatusCode::BAD_REQUEST, "Invalid UTF-8 sequence").into_response(),
        },
        Err(_) => return (StatusCode::BAD_REQUEST, "Invalid Base64 path").into_response(),
    };

    match tokio::fs::read(&decoded_path).await {
        Ok(body) => {
            let mime_type = mime_guess::from_path(&decoded_path)
                .first_or_octet_stream()
                .to_string();
            Response::builder()
                .status(StatusCode::OK)
                .header(header::CONTENT_TYPE, mime_type)
                .body(axum::body::Body::from(body))
                .unwrap()
                .into_response()
        }
        Err(_) => (StatusCode::NOT_FOUND, "Image not found").into_response(),
    }
}

pub async fn rescan(
    State(db_pool): State<Arc<Mutex<Connection>>>,
) -> Result<StatusCode, StatusCode> {
    log::info!("Rescan request received.");

    // 清空 photos 表
    {
        let conn = db_pool.lock().unwrap();
        conn.execute("DELETE FROM photos", [])
            .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    }

    // 在后台启动扫描任务
    let db_clone = Arc::clone(&db_pool);
    tokio::spawn(async move {
        crate::scanner::scan_directories("./test_images", db_clone);
    });

    Ok(StatusCode::ACCEPTED)
}
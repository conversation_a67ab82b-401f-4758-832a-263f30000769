use axum::{routing::{get, post}, Router};
use std::net::SocketAddr;
use std::sync::{Arc, Mutex};
use tower_http::services::ServeDir;

mod database;
mod handlers;
mod scanner;
mod thumbnail;

#[tokio::main]
async fn main() {
    // 初始化日志记录
    env_logger::init();

    // 初始化数据库
    let conn = database::initialize_database().expect("Failed to initialize database");
    log::info!("Database initialized successfully.");
    let db_pool = Arc::new(Mutex::new(conn));

    // 在后台启动扫描任务
    let db_clone = Arc::clone(&db_pool);
    tokio::spawn(async move {
        // 在实际应用中，这个路径应该来自配置或环境变量
        scanner::scan_directories("./test_images", db_clone);
    });

    // 构建应用路由
    let app = Router::new()
        .route("/", get(handler))
        .route("/api/folders", get(handlers::get_folders))
        .route("/api/photos", get(handlers::get_photos))
        .route("/photo/:path", get(handlers::get_photo))
        .route("/api/rescan", post(handlers::rescan))
        .nest_service("/", ServeDir::new("frontend/build"))
        .with_state(db_pool);

    // 定义监听地址
    let addr = SocketAddr::from(([0, 0, 0, 0], 3000));
    log::info!("listening on {}", addr);

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(addr).await.unwrap();
    axum::serve(listener, app).await.unwrap();
}

async fn handler() -> &'static str {
    "Hello, World!"
}
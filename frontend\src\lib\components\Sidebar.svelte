<script>
  import { onMount } from 'svelte';
  import { selectedFolder } from '$lib/stores.js';

  let folders = [];
  let error = null;
  let currentFolder = null;
  let isScanning = false;

  selectedFolder.subscribe(value => {
    currentFolder = value;
  });

  function selectFolder(folder) {
    selectedFolder.set(folder);
  }

  async function handleRescan() {
    isScanning = true;
    try {
      const response = await fetch('/api/rescan', { method: 'POST' });
      if (response.status === 202) {
        // Accepted. Give it a moment for the scan to start and then reload.
        alert('Scan started. The page will reload in a few seconds.');
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        throw new Error('Failed to start rescan.');
      }
    } catch (e) {
      alert(e.message);
      isScanning = false;
    }
  }

  onMount(async () => {
    try {
      const response = await fetch('/api/folders');
      if (!response.ok) {
        throw new Error('Failed to fetch folders');
      }
      folders = await response.json();
    } catch (e) {
      error = e.message;
    }
  });
</script>

<div class="sidebar-container">
  <div class="sidebar-header">
    <h2>Folders</h2>
    <button class="rescan-btn" on:click={handleRescan} disabled={isScanning}>
      {isScanning ? 'Scanning...' : 'Rescan'}
    </button>
  </div>
  {#if error}
    <p class="error">{error}</p>
  {:else if folders.length === 0}
    <p>No folders found. Try scanning.</p>
  {:else}
    <ul class="folder-list">
      {#each folders as folder}
        <li
          class="folder-item"
          class:active={currentFolder && currentFolder.path === folder.path}
          on:click={() => selectFolder(folder)}
        >
          <img src={folder.cover_path} alt="Cover for {folder.path}" class="cover-image" />
          <span class="folder-name">{folder.path.split(/[\\/]/).pop()}</span>
        </li>
      {/each}
    </ul>
  {/if}
</div>

<style>
  .sidebar-container {
    padding: 1rem;
  }
  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  .sidebar-header h2 {
    margin: 0;
  }
  .rescan-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  .rescan-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
  .error {
    color: red;
  }
  .folder-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  .folder-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  .folder-item:hover {
    background-color: #f0f0f0;
  }
  .folder-item.active {
    background-color: #e6f7ff;
    font-weight: bold;
  }
  .cover-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    margin-right: 0.75rem;
    border-radius: 4px;
  }
  .folder-name {
    font-size: 0.9rem;
  }
</style>